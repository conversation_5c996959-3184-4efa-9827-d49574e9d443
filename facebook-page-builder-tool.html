<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Business Page Builder Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        
        .neomorphism {
            background: #f1f5f9;
            border-radius: 16px;
            box-shadow: 
                8px 8px 16px rgba(148, 163, 184, 0.3),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-inset {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                inset 4px 4px 8px rgba(148, 163, 184, 0.3),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-button {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                4px 4px 8px rgba(148, 163, 184, 0.3),
                -4px -4px 8px rgba(255, 255, 255, 0.8);
            transition: all 0.2s ease;
        }
        
        .neomorphism-button:hover {
            box-shadow: 
                2px 2px 4px rgba(148, 163, 184, 0.3),
                -2px -2px 4px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-button:active {
            box-shadow: 
                inset 2px 2px 4px rgba(148, 163, 184, 0.3),
                inset -2px -2px 4px rgba(255, 255, 255, 0.8);
        }
        
        .input-neomorphism {
            background: #f1f5f9;
            border: none;
            border-radius: 12px;
            box-shadow: 
                inset 4px 4px 8px rgba(148, 163, 184, 0.3),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
            padding: 12px 16px;
            outline: none;
            transition: all 0.2s ease;
        }
        
        .input-neomorphism:focus {
            box-shadow: 
                inset 2px 2px 4px rgba(148, 163, 184, 0.4),
                inset -2px -2px 4px rgba(255, 255, 255, 0.9),
                0 0 0 2px rgba(59, 130, 246, 0.3);
        }
        
        .preview-frame {
            background: white;
            border-radius: 16px;
            box-shadow: 
                8px 8px 20px rgba(148, 163, 184, 0.3),
                -8px -8px 20px rgba(255, 255, 255, 0.8);
            height: 600px;
            overflow-y: auto;
        }
        
        .facebook-blue {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
        }
    </style>
</head>
<body class="min-h-screen p-6">
    
    <!-- Main Container -->
    <div class="max-w-7xl mx-auto">
        
        <!-- Header -->
        <div class="neomorphism p-8 mb-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">Facebook Business Page Builder</h1>
                <p class="text-lg text-gray-600">Create your professional Facebook shop page in minutes</p>
            </div>
        </div>

        <!-- Main Builder Interface -->
        <div class="grid grid-cols-12 gap-6">
            
            <!-- Left Panel - Form Controls -->
            <div class="col-span-4 space-y-6">
                
                <!-- Business Information -->
                <div class="neomorphism p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Business Information</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
                            <input type="text" id="businessName" class="input-neomorphism w-full" placeholder="Enter your business name" value="Chanta">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Business Emoji</label>
                            <input type="text" id="businessEmoji" class="input-neomorphism w-full" placeholder="👜" value="👜" maxlength="2">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tagline</label>
                            <input type="text" id="businessTagline" class="input-neomorphism w-full" placeholder="Premium bags & accessories" value="Premium bags & accessories">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="text" id="businessPhone" class="input-neomorphism w-full" placeholder="(*************" value="(*************">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                            <input type="text" id="businessAddress" class="input-neomorphism w-full" placeholder="123 Main St, City, State" value="123 Fashion Ave, Style City, SC">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Store Hours</label>
                            <input type="text" id="businessHours" class="input-neomorphism w-full" placeholder="Mon-Fri 9AM-6PM" value="Mon-Fri 9AM-6PM">
                        </div>
                    </div>
                </div>

                <!-- Hero Section -->
                <div class="neomorphism p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Hero Section</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Hero Title</label>
                            <input type="text" id="heroTitle" class="input-neomorphism w-full" placeholder="Welcome to our store" value="Get Inspired">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Hero Subtitle</label>
                            <input type="text" id="heroSubtitle" class="input-neomorphism w-full" placeholder="Discover amazing products" value="Browsing for your next long-haul trip, everyday journey, or just fancy a look at what's new? From community favourites to about-to-sell-out items, see them all here.">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Hero Image Icon</label>
                            <select id="heroImage" class="input-neomorphism w-full">
                                <option value="shopping-bag">🛍️ Shopping Bag</option>
                                <option value="store">🏪 Store</option>
                                <option value="gift">🎁 Gift</option>
                                <option value="heart">❤️ Heart</option>
                                <option value="star">⭐ Star</option>
                                <option value="fire">🔥 Fire</option>
                                <option value="crown">👑 Crown</option>
                                <option value="diamond">💎 Diamond</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Products -->
                <div class="neomorphism p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Featured Products</h2>
                    <div class="space-y-6">
                        <!-- Product 1 -->
                        <div class="neomorphism-inset p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">Product 1</h3>
                            <div class="space-y-3">
                                <input type="text" id="product1Name" class="input-neomorphism w-full text-sm" placeholder="Product name" value="Shibuya Totepack">
                                <input type="text" id="product1Description" class="input-neomorphism w-full text-sm" placeholder="Product description" value="Recycled PET Rip Stop">
                                <input type="text" id="product1Price" class="input-neomorphism w-full text-sm" placeholder="Price" value="140.00">
                            </div>
                        </div>
                        
                        <!-- Product 2 -->
                        <div class="neomorphism-inset p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">Product 2</h3>
                            <div class="space-y-3">
                                <input type="text" id="product2Name" class="input-neomorphism w-full text-sm" placeholder="Product name" value="SoFo Backpack City">
                                <input type="text" id="product2Description" class="input-neomorphism w-full text-sm" placeholder="Product description" value="Recycled Coated Cotton Canvas">
                                <input type="text" id="product2Price" class="input-neomorphism w-full text-sm" placeholder="Price" value="280.00">
                            </div>
                        </div>
                        
                        <!-- Product 3 -->
                        <div class="neomorphism-inset p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">Product 3</h3>
                            <div class="space-y-3">
                                <input type="text" id="product3Name" class="input-neomorphism w-full text-sm" placeholder="Product name" value="Gion Backpack Pro">
                                <input type="text" id="product3Description" class="input-neomorphism w-full text-sm" placeholder="Product description" value="Waterproof Tarpaulin">
                                <input type="text" id="product3Price" class="input-neomorphism w-full text-sm" placeholder="Price" value="140.00">
                            </div>
                        </div>
                        
                        <!-- Product 4 -->
                        <div class="neomorphism-inset p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">Product 4</h3>
                            <div class="space-y-3">
                                <input type="text" id="product4Name" class="input-neomorphism w-full text-sm" placeholder="Product name" value="SoFo Rolltop Backpack X">
                                <input type="text" id="product4Description" class="input-neomorphism w-full text-sm" placeholder="Product description" value="Recycled Coated Cotton Canvas">
                                <input type="text" id="product4Price" class="input-neomorphism w-full text-sm" placeholder="Price" value="170.00">
                            </div>
                        </div>
                        
                        <!-- Product 5 -->
                        <div class="neomorphism-inset p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">Product 5</h3>
                            <div class="space-y-3">
                                <input type="text" id="product5Name" class="input-neomorphism w-full text-sm" placeholder="Product name" value="Kiso Messenger Bag">
                                <input type="text" id="product5Description" class="input-neomorphism w-full text-sm" placeholder="Product description" value="Organic Cotton Canvas">
                                <input type="text" id="product5Price" class="input-neomorphism w-full text-sm" placeholder="Price" value="95.00">
                            </div>
                        </div>
                        
                        <!-- Product 6 -->
                        <div class="neomorphism-inset p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">Product 6</h3>
                            <div class="space-y-3">
                                <input type="text" id="product6Name" class="input-neomorphism w-full text-sm" placeholder="Product name" value="Harajuku Daypack">
                                <input type="text" id="product6Description" class="input-neomorphism w-full text-sm" placeholder="Product description" value="Recycled Polyester Ripstop">
                                <input type="text" id="product6Price" class="input-neomorphism w-full text-sm" placeholder="Price" value="120.00">
                            </div>
                        </div>
                        
                        <!-- Product 7 -->
                        <div class="neomorphism-inset p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">Product 7</h3>
                            <div class="space-y-3">
                                <input type="text" id="product7Name" class="input-neomorphism w-full text-sm" placeholder="Product name" value="Ginza Travel Tote">
                                <input type="text" id="product7Description" class="input-neomorphism w-full text-sm" placeholder="Product description" value="Premium Leather Alternative">
                                <input type="text" id="product7Price" class="input-neomorphism w-full text-sm" placeholder="Price" value="160.00">
                            </div>
                        </div>
                        
                        <!-- Product 8 -->
                        <div class="neomorphism-inset p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">Product 8</h3>
                            <div class="space-y-3">
                                <input type="text" id="product8Name" class="input-neomorphism w-full text-sm" placeholder="Product name" value="Akihabara Tech Pack">
                                <input type="text" id="product8Description" class="input-neomorphism w-full text-sm" placeholder="Product description" value="Water-Resistant Nylon">
                                <input type="text" id="product8Price" class="input-neomorphism w-full text-sm" placeholder="Price" value="190.00">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- About Section -->
                <div class="neomorphism p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">About Section</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">About Text</label>
                            <textarea id="aboutText" class="input-neomorphism w-full h-20 resize-none" placeholder="Tell customers about your business">We are a premium bag and accessories brand dedicated to creating functional, stylish pieces for modern life. Our collection combines innovative design with sustainable materials.</textarea>
                        </div>
                        <div class="grid grid-cols-3 gap-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">Years</label>
                                <input type="text" id="yearsExperience" class="input-neomorphism w-full text-sm" placeholder="5" value="10">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">Customers</label>
                                <input type="text" id="happyCustomers" class="input-neomorphism w-full text-sm" placeholder="1000" value="5000">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">Products</label>
                                <input type="text" id="productsCount" class="input-neomorphism w-full text-sm" placeholder="50" value="500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">About Image Emoji</label>
                            <input type="text" id="aboutImage" class="input-neomorphism w-full" placeholder="🏪" value="🏪" maxlength="2">
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-4">
                    <button id="updatePreview" class="neomorphism-button w-full py-4 text-gray-700 font-semibold">
                        🔄 Update Preview
                    </button>
                    <button id="generatePage" class="facebook-blue w-full py-4 text-white font-semibold rounded-lg">
                        🚀 Generate Facebook Page
                    </button>
                </div>
            </div>

            <!-- Right Panel - Live Preview -->
            <div class="col-span-8">
                <div class="neomorphism p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Live Preview</h2>
                        <div class="flex space-x-2">
                            <button class="neomorphism-button px-4 py-2 text-sm">📱 Mobile</button>
                            <button class="neomorphism-button px-4 py-2 text-sm bg-blue-100">💻 Desktop</button>
                        </div>
                    </div>
                    
                    <!-- Template Selection -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Template Style</label>
                        <div class="flex space-x-2">
                            <button id="modernTemplate" class="neomorphism-button px-4 py-2 text-sm bg-blue-100">Modern Store</button>
                            <button id="classicTemplate" class="neomorphism-button px-4 py-2 text-sm">Classic Store</button>
                        </div>
                    </div>
                    
                    <!-- Preview Frame -->
                    <div class="preview-frame">
                        <iframe id="previewFrame" src="template-realistic-store.html" class="w-full h-full border-0 rounded-lg"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>    <script>
        // Template selection
        let currentTemplate = 'realistic';
        
        // Realistic store template
        const realisticTemplateHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{BUSINESS_NAME}} - Facebook Shop</title>
    <script src="https://cdn.tailwindcss.com"><\/script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
        }
        
        .neomorphism {
            background: #f1f5f9;
            border-radius: 16px;
            box-shadow: 
                8px 8px 16px rgba(148, 163, 184, 0.3),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-inset {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                inset 4px 4px 8px rgba(148, 163, 184, 0.3),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
        }
        
        .product-card {
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 
                4px 4px 12px rgba(148, 163, 184, 0.2),
                -4px -4px 12px rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 
                8px 8px 20px rgba(148, 163, 184, 0.3),
                -8px -8px 20px rgba(255, 255, 255, 0.9);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
            border-radius: 12px;
            box-shadow: 
                4px 4px 8px rgba(24, 119, 242, 0.3),
                -2px -2px 8px rgba(255, 255, 255, 0.8);
        }
        
        .btn-primary:hover {
            box-shadow: 
                2px 2px 4px rgba(24, 119, 242, 0.3),
                -1px -1px 4px rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    
    <!-- Header Section -->
    <div class="neomorphism mx-8 mt-8 p-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center">
                    <span class="text-2xl">{{BUSINESS_EMOJI}}</span>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">{{BUSINESS_NAME}}</h1>
                    <p class="text-gray-600">{{BUSINESS_TAGLINE}}</p>
                </div>
            </div>
            <div class="flex space-x-4">
                <button class="btn-primary text-white px-6 py-3 font-semibold">
                    💬 Message Us
                </button>
                <button class="neomorphism px-6 py-3 text-gray-700 font-semibold">
                    ⭐ Follow
                </button>
            </div>
        </div>
    </div>

    <!-- Hero Banner -->
    <div class="neomorphism mx-8 mt-8 p-8">
        <div class="text-center">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">{{HERO_TITLE}}</h2>
            <p class="text-xl text-gray-600 mb-8">{{HERO_SUBTITLE}}</p>
            <div class="w-full h-64 neomorphism-inset rounded-lg bg-gradient-to-r from-blue-100 to-purple-100 flex items-center justify-center">
                <span class="text-6xl">{{HERO_IMAGE_PLACEHOLDER}}</span>
            </div>
        </div>
    </div>

    <!-- Categories -->
    <div class="mx-8 mt-8">
        <h3 class="text-2xl font-bold text-gray-800 mb-6">Shop by Category</h3>
        <div class="grid grid-cols-4 gap-6">
            <div class="neomorphism p-6 text-center cursor-pointer hover:transform hover:scale-105 transition-transform">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">👕</span>
                </div>
                <span class="font-semibold text-gray-700">Clothing</span>
            </div>
            <div class="neomorphism p-6 text-center cursor-pointer hover:transform hover:scale-105 transition-transform">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">👠</span>
                </div>
                <span class="font-semibold text-gray-700">Shoes</span>
            </div>
            <div class="neomorphism p-6 text-center cursor-pointer hover:transform hover:scale-105 transition-transform">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">👜</span>
                </div>
                <span class="font-semibold text-gray-700">Accessories</span>
            </div>
            <div class="neomorphism p-6 text-center cursor-pointer hover:transform hover:scale-105 transition-transform">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🎁</span>
                </div>
                <span class="font-semibold text-gray-700">Gifts</span>
            </div>
        </div>
    </div>

    <!-- Featured Products -->
    <div class="mx-8 mt-8">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-2xl font-bold text-gray-800">Featured Products</h3>
            <button class="text-blue-600 font-semibold hover:underline">View All</button>
        </div>
        <div class="grid grid-cols-3 gap-6">
            <!-- Product 1 -->
            <div class="product-card p-6">
                <div class="w-full h-48 bg-gradient-to-br from-pink-100 to-pink-200 rounded-lg mb-4 flex items-center justify-center">
                    <span class="text-4xl">👕</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">{{PRODUCT_1_NAME}}</h4>
                <p class="text-gray-600 text-sm mb-4">{{PRODUCT_1_DESCRIPTION}}</p>
                <div class="flex justify-between items-center">
                    <span class="text-2xl font-bold text-blue-600">$\{{PRODUCT_1_PRICE}}</span>
                    <button class="btn-primary text-white px-4 py-2 text-sm font-semibold">
                        Add to Cart
                    </button>
                </div>
            </div>
            
            <!-- Product 2 -->
            <div class="product-card p-6">
                <div class="w-full h-48 bg-gradient-to-br from-green-100 to-green-200 rounded-lg mb-4 flex items-center justify-center">
                    <span class="text-4xl">👠</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">{{PRODUCT_2_NAME}}</h4>
                <p class="text-gray-600 text-sm mb-4">{{PRODUCT_2_DESCRIPTION}}</p>
                <div class="flex justify-between items-center">
                    <span class="text-2xl font-bold text-blue-600">$\{{PRODUCT_2_PRICE}}</span>
                    <button class="btn-primary text-white px-4 py-2 text-sm font-semibold">
                        Add to Cart
                    </button>
                </div>
            </div>
            
            <!-- Product 3 -->
            <div class="product-card p-6">
                <div class="w-full h-48 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg mb-4 flex items-center justify-center">
                    <span class="text-4xl">👜</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">{{PRODUCT_3_NAME}}</h4>
                <p class="text-gray-600 text-sm mb-4">{{PRODUCT_3_DESCRIPTION}}</p>
                <div class="flex justify-between items-center">
                    <span class="text-2xl font-bold text-blue-600">$\{{PRODUCT_3_PRICE}}</span>
                    <button class="btn-primary text-white px-4 py-2 text-sm font-semibold">
                        Add to Cart
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- About Section -->
    <div class="neomorphism mx-8 mt-8 p-8">
        <div class="grid grid-cols-2 gap-8 items-center">
            <div>
                <h3 class="text-2xl font-bold text-gray-800 mb-4">About {{BUSINESS_NAME}}</h3>
                <p class="text-gray-600 mb-6">{{ABOUT_TEXT}}</p>
                <div class="flex space-x-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{YEARS_EXPERIENCE}}+</div>
                        <div class="text-sm text-gray-600">Years Experience</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{HAPPY_CUSTOMERS}}+</div>
                        <div class="text-sm text-gray-600">Happy Customers</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{PRODUCTS_COUNT}}+</div>
                        <div class="text-sm text-gray-600">Products</div>
                    </div>
                </div>
            </div>
            <div class="neomorphism-inset rounded-lg h-64 flex items-center justify-center">
                <span class="text-6xl">{{ABOUT_IMAGE_PLACEHOLDER}}</span>
            </div>
        </div>
    </div>

    <!-- Contact & Location -->
    <div class="neomorphism mx-8 mt-8 mb-8 p-8">
        <div class="grid grid-cols-3 gap-8 text-center">
            <div>
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📍</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">Visit Our Store</h4>
                <p class="text-gray-600">{{BUSINESS_ADDRESS}}</p>
            </div>
            <div>
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📞</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">Call Us</h4>
                <p class="text-gray-600">{{BUSINESS_PHONE}}</p>
            </div>
            <div>
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">⏰</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">Store Hours</h4>
                <p class="text-gray-600">{{BUSINESS_HOURS}}</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const addToCartButtons = document.querySelectorAll('.btn-primary');
            
            addToCartButtons.forEach(button => {
                if (button.textContent.includes('Add to Cart')) {
                    button.addEventListener('click', function() {
                        this.textContent = '✅ Added!';
                        this.style.background = 'linear-gradient(135deg, #10b981, #34d399)';
                        
                        setTimeout(() => {
                            this.textContent = 'Add to Cart';
                            this.style.background = 'linear-gradient(135deg, #1877f2, #42a5f5)';
                        }, 2000);
                    });
                }
            });
        });
    <\/script>

</body>
</html>`;

        // Function to get form values
        function getFormData() {
            return {
                BUSINESS_NAME: document.getElementById('businessName').value,
                BUSINESS_EMOJI: document.getElementById('businessEmoji').value,
                BUSINESS_TAGLINE: document.getElementById('businessTagline').value,
                BUSINESS_PHONE: document.getElementById('businessPhone').value,
                BUSINESS_ADDRESS: document.getElementById('businessAddress').value,
                BUSINESS_HOURS: document.getElementById('businessHours').value,
                HERO_TITLE: document.getElementById('heroTitle').value,
                HERO_SUBTITLE: document.getElementById('heroSubtitle').value,
                HERO_IMAGE_PLACEHOLDER: document.getElementById('heroImage').value,
                PRODUCT_1_NAME: document.getElementById('product1Name').value,
                PRODUCT_1_DESCRIPTION: document.getElementById('product1Description').value,
                PRODUCT_1_PRICE: document.getElementById('product1Price').value,
                PRODUCT_2_NAME: document.getElementById('product2Name').value,
                PRODUCT_2_DESCRIPTION: document.getElementById('product2Description').value,
                PRODUCT_2_PRICE: document.getElementById('product2Price').value,
                PRODUCT_3_NAME: document.getElementById('product3Name').value,
                PRODUCT_3_DESCRIPTION: document.getElementById('product3Description').value,
                PRODUCT_3_PRICE: document.getElementById('product3Price').value,
                ABOUT_TEXT: document.getElementById('aboutText').value,
                YEARS_EXPERIENCE: document.getElementById('yearsExperience').value,
                HAPPY_CUSTOMERS: document.getElementById('happyCustomers').value,
                PRODUCTS_COUNT: document.getElementById('productsCount').value,
                ABOUT_IMAGE_PLACEHOLDER: document.getElementById('aboutImage').value
            };
        }

        // Function to replace placeholders in template
        function generateHTML(data) {
            let html = templateHTML;
            Object.keys(data).forEach(key => {
                const regex = new RegExp(`{{${key}}}`, 'g');
                html = html.replace(regex, data[key]);
            });
            return html;
        }

        // Function to update preview
        function updatePreview() {
            const data = getFormData();
            const generatedHTML = generateHTML(data);
            
            // Create a blob URL for the generated HTML
            const blob = new Blob([generatedHTML], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            
            // Update the iframe
            const iframe = document.getElementById('previewFrame');
            iframe.src = url;
            
            // Clean up the previous blob URL after a delay
            setTimeout(() => {
                if (iframe.dataset.previousUrl) {
                    URL.revokeObjectURL(iframe.dataset.previousUrl);
                }
                iframe.dataset.previousUrl = url;
            }, 1000);
        }

        // Function to generate and download the page
        function generatePage() {
            const data = getFormData();
            const generatedHTML = generateHTML(data);
            
            // Create download
            const blob = new Blob([generatedHTML], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${data.BUSINESS_NAME.replace(/\s+/g, '-').toLowerCase()}-facebook-page.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            // Show success message
            const generateBtn = document.getElementById('generatePage');
            const originalText = generateBtn.innerHTML;
            generateBtn.innerHTML = '✅ Page Generated!';
            generateBtn.style.background = 'linear-gradient(135deg, #10b981, #34d399)';
            
            setTimeout(() => {
                generateBtn.innerHTML = originalText;
                generateBtn.style.background = 'linear-gradient(135deg, #1877f2, #42a5f5)';
            }, 3000);
        }

        // Function to populate form from URL parameters
        function populateFromURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            
            // Populate all form fields if parameters exist
            const fieldMappings = {
                'businessName': 'businessName',
                'businessEmoji': 'businessEmoji',
                'businessTagline': 'businessTagline',
                'businessPhone': 'businessPhone',
                'businessAddress': 'businessAddress',
                'businessHours': 'businessHours',
                'heroTitle': 'heroTitle',
                'heroSubtitle': 'heroSubtitle',
                'heroImage': 'heroImage',
                'aboutText': 'aboutText',
                'yearsExperience': 'yearsExperience',
                'happyCustomers': 'happyCustomers',
                'productsCount': 'productsCount',
                'aboutImage': 'aboutImage',
                'product1Name': 'product1Name',
                'product1Description': 'product1Description',
                'product1Price': 'product1Price',
                'product2Name': 'product2Name',
                'product2Description': 'product2Description',
                'product2Price': 'product2Price',
                'product3Name': 'product3Name',
                'product3Description': 'product3Description',
                'product3Price': 'product3Price'
            };
            
            let hasParams = false;
            Object.entries(fieldMappings).forEach(([param, fieldId]) => {
                const value = urlParams.get(param);
                if (value) {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        field.value = decodeURIComponent(value);
                        hasParams = true;
                    }
                }
            });
            
            // Show notification if data was imported
            if (hasParams) {
                showImportNotification();
            }
        }
        
        // Function to show import notification
        function showImportNotification() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = '✅ Data imported from Facebook page!';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Populate from URL parameters first
            populateFromURLParams();
            
            // Update preview button
            document.getElementById('updatePreview').addEventListener('click', updatePreview);
            
            // Generate page button
            document.getElementById('generatePage').addEventListener('click', generatePage);
            
            // Auto-update preview on input changes (with debounce)
            let debounceTimer;
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(updatePreview, 500);
                });
            });
            
            // Template selection
            document.getElementById('modernTemplate').addEventListener('click', function() {
                document.getElementById('modernTemplate').classList.add('bg-blue-100');
                document.getElementById('classicTemplate').classList.remove('bg-blue-100');
                document.getElementById('previewFrame').src = 'template-realistic-store.html';
                setTimeout(updatePreview, 1000);
            });
            
            document.getElementById('classicTemplate').addEventListener('click', function() {
                document.getElementById('classicTemplate').classList.add('bg-blue-100');
                document.getElementById('modernTemplate').classList.remove('bg-blue-100');
                document.getElementById('previewFrame').src = 'template-retail-store.html';
                setTimeout(updatePreview, 1000);
            });
            
            // Initial preview update (delayed to allow URL parameter population)
            setTimeout(updatePreview, 1500);
        });
    </script>
</body>
</html>