<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Business Suite - Page Builder</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        
        .neomorphism {
            background: #f1f5f9;
            border-radius: 16px;
            box-shadow: 
                8px 8px 16px rgba(148, 163, 184, 0.3),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-button {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                4px 4px 8px rgba(148, 163, 184, 0.3),
                -4px -4px 8px rgba(255, 255, 255, 0.8);
            transition: all 0.2s ease;
        }
        
        .neomorphism-button:hover {
            transform: translateY(-2px);
            box-shadow: 
                8px 8px 16px rgba(148, 163, 184, 0.3),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
        }
        
        .facebook-blue {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
        }
        
        .feature-card {
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 
                4px 4px 12px rgba(148, 163, 184, 0.2),
                -4px -4px 12px rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 
                8px 8px 20px rgba(148, 163, 184, 0.3),
                -8px -8px 20px rgba(255, 255, 255, 0.9);
        }
    </style>
</head>
<body class="min-h-screen p-6">
    
    <!-- Main Container -->
    <div class="max-w-6xl mx-auto">
        
        <!-- Header -->
        <div class="neomorphism p-8 mb-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">Facebook Business Suite</h1>
                <p class="text-xl text-gray-600 mb-6">Complete toolkit for creating professional Facebook business pages</p>
                <div class="flex justify-center items-center space-x-4">
                    <div class="text-sm text-gray-500">Powered by</div>
                    <div class="facebook-blue text-white px-4 py-2 rounded-lg font-semibold text-sm">AI Prompt Builder</div>
                </div>
            </div>
        </div>

        <!-- Main Tools Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            
            <!-- Tool 1: Page Importer -->
            <div class="feature-card p-6">
                <div class="text-center">
                    <div class="w-16 h-16 neomorphism rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🔗</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">Import Existing Page</h3>
                    <p class="text-gray-600 mb-6">Extract data from your existing Facebook business page or use sample templates</p>
                    <button onclick="openTool('facebook-importer.html')" class="neomorphism-button w-full py-3 text-gray-700 font-semibold">
                        🚀 Start Import
                    </button>
                </div>
            </div>

            <!-- Tool 2: Page Builder -->
            <div class="feature-card p-6">
                <div class="text-center">
                    <div class="w-16 h-16 neomorphism rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🛠️</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">Build New Page</h3>
                    <p class="text-gray-600 mb-6">Create a brand new Facebook business page from scratch with live preview</p>
                    <button onclick="openTool('facebook-page-builder-tool.html')" class="neomorphism-button w-full py-3 text-gray-700 font-semibold">
                        ✨ Start Building
                    </button>
                </div>
            </div>

            <!-- Tool 3: Prompt Builder -->
            <div class="feature-card p-6">
                <div class="text-center">
                    <div class="w-16 h-16 neomorphism rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🧠</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">AI Prompt Builder</h3>
                    <p class="text-gray-600 mb-6">Advanced AI prompt builder for creating custom web interfaces and designs</p>
                    <button onclick="openTool('index.html')" class="neomorphism-button w-full py-3 text-gray-700 font-semibold">
                        🎯 Build Prompts
                    </button>
                </div>
            </div>
        </div>

        <!-- Workflow Section -->
        <div class="neomorphism p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Recommended Workflow</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="w-12 h-12 neomorphism rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-xl font-bold text-blue-600">1</span>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-2">Import or Start Fresh</h3>
                    <p class="text-gray-600 text-sm">Use the importer to extract data from an existing page or start with sample templates</p>
                </div>

                <!-- Step 2 -->
                <div class="text-center">
                    <div class="w-12 h-12 neomorphism rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-xl font-bold text-blue-600">2</span>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-2">Customize & Build</h3>
                    <p class="text-gray-600 text-sm">Use the page builder to customize your business information and see live preview</p>
                </div>

                <!-- Step 3 -->
                <div class="text-center">
                    <div class="w-12 h-12 neomorphism rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-xl font-bold text-blue-600">3</span>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-2">Generate & Deploy</h3>
                    <p class="text-gray-600 text-sm">Download your completed Facebook business page and deploy it to your platform</p>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            
            <!-- Features List -->
            <div class="neomorphism p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">✨ Key Features</h3>
                <ul class="space-y-3">
                    <li class="flex items-center">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-gray-700">Auto-import from existing Facebook pages</span>
                    </li>
                    <li class="flex items-center">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-gray-700">Live preview while building</span>
                    </li>
                    <li class="flex items-center">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-gray-700">Neumorphism design with 8pt grid</span>
                    </li>
                    <li class="flex items-center">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-gray-700">Professional templates included</span>
                    </li>
                    <li class="flex items-center">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-gray-700">Export ready-to-use HTML</span>
                    </li>
                    <li class="flex items-center">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-gray-700">Advanced AI prompt generation</span>
                    </li>
                </ul>
            </div>

            <!-- Sample Templates -->
            <div class="neomorphism p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Sample Templates
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center p-3 bg-white rounded-lg">
                        <div class="w-12 h-12 mr-3 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-800">Fashion Store</div>
                            <div class="text-sm text-gray-600">Trendy Fashion Boutique</div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-white rounded-lg">
                        <div class="w-12 h-12 mr-3 bg-orange-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-800">Restaurant</div>
                            <div class="text-sm text-gray-600">Mario's Italian Kitchen</div>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-white rounded-lg">
                        <div class="w-12 h-12 mr-3 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-800">Tech Services</div>
                            <div class="text-sm text-gray-600">Digital Solutions Pro</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Start Section -->
        <div class="neomorphism p-8 text-center">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Ready to Get Started?</h2>
            <p class="text-gray-600 mb-6">Choose your preferred starting point and create your professional Facebook business page in minutes</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="openTool('facebook-importer.html')" class="facebook-blue text-white px-8 py-3 rounded-lg font-semibold">
                    🔗 Import Existing Page
                </button>
                <button onclick="openTool('facebook-page-builder-tool.html')" class="neomorphism-button px-8 py-3 text-gray-700 font-semibold">
                    🛠️ Build From Scratch
                </button>
            </div>
        </div>
    </div>

    <script>
        function openTool(toolUrl) {
            window.open(toolUrl, '_blank');
        }
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add click animations to buttons
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>

</body>
</html>